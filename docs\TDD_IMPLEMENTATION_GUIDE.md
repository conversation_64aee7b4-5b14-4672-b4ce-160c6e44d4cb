# Test-Driven Development Implementation Guide for RAG Chatbot

## 🎯 Purpose

This document provides a comprehensive TDD strategy for developing and maintaining a robust RAG-powered chatbot that can handle diverse user queries with high reliability and performance.

## 📋 Table of Contents

1. [TDD Philosophy for RAG Systems](#tdd-philosophy)
2. [Test Architecture Overview](#test-architecture)
3. [Core Test Suites](#core-test-suites)
4. [Implementation Examples](#implementation-examples)
5. [TDD Workflow](#tdd-workflow)
6. [Quality Gates](#quality-gates)
7. [AI Agent Instructions](#ai-agent-instructions)

## 🧠 TDD Philosophy for RAG Systems {#tdd-philosophy}

### Core Principles

1. **Query-First Development**: Write tests for expected query behaviors before implementing retrieval logic
2. **Intent-Driven Testing**: Test based on user intent rather than technical implementation
3. **Context-Aware Validation**: Ensure conversation context is properly maintained across turns
4. **Fallback Reliability**: Test graceful degradation when primary strategies fail
5. **Performance Boundaries**: Establish and maintain response time and accuracy thresholds

### RAG-Specific Testing Challenges

- **Dynamic Content**: Database content changes affect test outcomes
- **Context Dependency**: Multi-turn conversations require stateful testing
- **Intent Ambiguity**: Same query can have different meanings in different contexts
- **Retrieval Variability**: Search results may vary based on data updates

## 🏗️ Test Architecture Overview {#test-architecture}

```
tests/
├── unit/
│   ├── ragService.test.ts          # Core RAG logic
│   ├── queryProcessor.test.ts      # Query preprocessing
│   └── intentDetection.test.ts     # Intent classification
├── integration/
│   ├── chatAPI.test.ts             # End-to-end API tests
│   └── conversationFlow.test.ts    # Multi-turn scenarios
├── performance/
│   ├── responseTime.test.ts        # Latency requirements
│   └── concurrency.test.ts         # Load handling
├── edge-cases/
│   ├── malformedQueries.test.ts    # Error handling
│   └── boundaryConditions.test.ts  # Limit testing
└── fixtures/
    ├── mockData.ts                 # Test data
    └── conversationScenarios.ts    # Conversation templates
```

## 🧪 Core Test Suites {#core-test-suites}

### 1. RAG Service Unit Tests

**File**: `src/lib/__tests__/ragService.test.ts`

```typescript
import { ragService } from '../ragService';
import { mockProducts } from '../../fixtures/mockData';

describe('RAG Service Core Functionality', () => {
  beforeEach(() => {
    // Setup test database state
    jest.clearAllMocks();
  });

  describe('Intent Detection', () => {
    const intentTestCases = [
      {
        query: 'what products do you offer',
        expectedIntent: 'catalog_browse',
        expectedStrategy: 'catalog_browse',
        description: 'should detect catalog browsing intent'
      },
      {
        query: 'hydraulic pump 5HP',
        expectedIntent: 'product_search',
        expectedStrategy: 'exact_phrase',
        description: 'should detect specific product search'
      },
      {
        query: 'compare pump A vs pump B',
        expectedIntent: 'comparison',
        expectedStrategy: 'multi_term',
        description: 'should detect comparison intent'
      },
      {
        query: 'is this in stock',
        expectedIntent: 'availability',
        expectedStrategy: 'context_aware',
        description: 'should detect availability inquiry'
      }
    ];

    test.each(intentTestCases)('$description', async ({ query, expectedIntent, expectedStrategy }) => {
      const result = await ragService.retrieveRelevantProducts(query);
      
      expect(result.intent).toBe(expectedIntent);
      expect(result.retrievalStrategy).toBe(expectedStrategy);
      expect(result.products).toBeDefined();
    });
  });

  describe('Multi-Strategy Retrieval', () => {
    it('should use exact phrase matching for specific queries', async () => {
      const result = await ragService.retrieveRelevantProducts('Caterpillar 320D excavator');
      
      expect(result.retrievalStrategy).toBe('exact_phrase');
      expect(result.products.length).toBeGreaterThan(0);
      expect(result.searchTerms).toEqual(['Caterpillar', '320D', 'excavator']);
    });

    it('should fallback to multi-term search when exact phrase fails', async () => {
      // Mock exact phrase to return no results
      jest.spyOn(ragService, 'exactPhraseSearch').mockResolvedValue([]);
      
      const result = await ragService.retrieveRelevantProducts('rare equipment model');
      
      expect(result.retrievalStrategy).toBe('multi_term');
      expect(result.products).toBeDefined();
    });

    it('should use single-term fallback as last resort', async () => {
      // Mock both exact and multi-term to fail
      jest.spyOn(ragService, 'exactPhraseSearch').mockResolvedValue([]);
      jest.spyOn(ragService, 'multiTermSearch').mockResolvedValue([]);
      
      const result = await ragService.retrieveRelevantProducts('pump');
      
      expect(result.retrievalStrategy).toBe('single_term');
      expect(result.products).toBeDefined();
    });
  });

  describe('Context-Aware Processing', () => {
    it('should treat catalog queries as independent', async () => {
      const context = {
        lastQuery: 'hydraulic pumps',
        conversationTurn: 2,
        previousProducts: mockProducts.hydraulicPumps
      };

      const result = await ragService.retrieveRelevantProductsWithContext(
        'what products do you offer',
        context
      );

      expect(result.retrievalStrategy).toBe('catalog_browse');
      expect(result.isIndependentQuery).toBe(true);
    });

    it('should use context for follow-up questions', async () => {
      const context = {
        lastQuery: 'show me generators',
        conversationTurn: 2,
        previousProducts: mockProducts.generators
      };

      const result = await ragService.retrieveRelevantProductsWithContext(
        'which one is most fuel efficient',
        context
      );

      expect(result.retrievalStrategy).toBe('context_aware');
      expect(result.isIndependentQuery).toBe(false);
      expect(result.products).toEqual(mockProducts.generators);
    });
  });

  describe('Error Handling & Resilience', () => {
    it('should handle database connection errors gracefully', async () => {
      // Mock database error
      jest.spyOn(ragService, 'queryDatabase').mockRejectedValue(new Error('DB Connection failed'));

      const result = await ragService.retrieveRelevantProducts('hydraulic pump');

      expect(result.retrievalStrategy).toBe('error_fallback');
      expect(result.products).toEqual(mockProducts.featured);
      expect(result.error).toBeDefined();
    });

    it('should handle malformed queries', async () => {
      const malformedQueries = ['', '   ', '@#$%^&*', '🚀🔥💯'];

      for (const query of malformedQueries) {
        const result = await ragService.retrieveRelevantProducts(query);
        expect(result.products).toBeDefined();
        expect(result.retrievalStrategy).toBe('featured_fallback');
      }
    });
  });

  describe('Performance Validation', () => {
    it('should respect maxResults configuration', async () => {
      const result = await ragService.retrieveRelevantProducts('pump', { maxResults: 5 });
      expect(result.products.length).toBeLessThanOrEqual(5);
    });

    it('should complete within acceptable time limits', async () => {
      const start = Date.now();
      await ragService.retrieveRelevantProducts('hydraulic pump');
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(2000); // 2 second SLA
    });
  });
});
```

### 2. Chat API Integration Tests

**File**: `src/app/api/chat/__tests__/route.test.ts`

```typescript
import { POST } from '../route';
import { NextRequest } from 'next/server';

describe('Chat API Integration', () => {
  const createRequest = (body: any) => {
    return new NextRequest('http://localhost/api/chat', {
      method: 'POST',
      body: JSON.stringify(body),
      headers: { 'Content-Type': 'application/json' }
    });
  };

  describe('Query Processing Flow', () => {
    it('should process catalog browse requests correctly', async () => {
      const request = createRequest({
        message: 'what products do you offer',
        history: []
      });

      const response = await POST(request);

      expect(response.status).toBe(200);
      expect(response.headers.get('X-RAG-Strategy')).toBe('catalog_browse');
      expect(response.headers.get('X-RAG-Results')).not.toBe('0');
    });

    it('should maintain conversation context', async () => {
      const history = [
        { sender: 'user', text: 'show me hydraulic pumps' },
        { sender: 'bot', text: 'Here are our hydraulic pumps...' }
      ];

      const request = createRequest({
        message: 'which one has the highest pressure rating',
        history
      });

      const response = await POST(request);

      expect(response.status).toBe(200);
      expect(response.headers.get('X-RAG-Strategy')).toBe('context_aware');
    });

    it('should handle streaming responses', async () => {
      const request = createRequest({
        message: 'tell me about your generators',
        history: []
      });

      const response = await POST(request);
      const reader = response.body?.getReader();

      expect(reader).toBeDefined();
      
      const { value, done } = await reader!.read();
      expect(value).toBeInstanceOf(Uint8Array);
      expect(done).toBe(false);
    });
  });

  describe('Error Scenarios', () => {
    it('should handle missing message gracefully', async () => {
      const request = createRequest({ history: [] });

      const response = await POST(request);

      expect(response.status).toBe(400);
    });

    it('should handle invalid JSON', async () => {
      const request = new NextRequest('http://localhost/api/chat', {
        method: 'POST',
        body: 'invalid json',
        headers: { 'Content-Type': 'application/json' }
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
    });

    it('should handle API key errors', async () => {
      // Mock missing API key
      const originalKey = process.env.GOOGLE_API_KEY;
      delete process.env.GOOGLE_API_KEY;

      const request = createRequest({
        message: 'test query',
        history: []
      });

      const response = await POST(request);

      expect(response.status).toBe(500);
      
      // Restore API key
      process.env.GOOGLE_API_KEY = originalKey;
    });
  });
});
```

### 3. Query Pattern Comprehensive Tests

**File**: `src/__tests__/queryPatterns.test.ts`

```typescript
import { ragService } from '@/lib/ragService';

describe('Comprehensive Query Pattern Testing', () => {
  describe('Intent Classification', () => {
    const intentTestCases = [
      // Catalog browsing patterns
      { query: 'what products do you offer', expectedIntent: 'catalog_browse' },
      { query: 'show me your full catalog', expectedIntent: 'catalog_browse' },
      { query: 'what do you sell', expectedIntent: 'catalog_browse' },
      { query: 'browse products', expectedIntent: 'catalog_browse' },
      
      // Product search patterns
      { query: 'hydraulic pump', expectedIntent: 'product_search' },
      { query: 'Caterpillar 320D excavator', expectedIntent: 'product_search' },
      { query: 'industrial generator 50kW', expectedIntent: 'product_search' },
      
      // Comparison patterns
      { query: 'compare pump A vs pump B', expectedIntent: 'comparison' },
      { query: 'difference between model X and Y', expectedIntent: 'comparison' },
      { query: 'which is better', expectedIntent: 'comparison' },
      
      // Pricing patterns
      { query: 'how much does it cost', expectedIntent: 'pricing' },
      { query: 'what is the price', expectedIntent: 'pricing' },
      { query: 'pricing information', expectedIntent: 'pricing' },
      
      // Availability patterns
      { query: 'is this in stock', expectedIntent: 'availability' },
      { query: 'do you have this available', expectedIntent: 'availability' },
      { query: 'when will it be available', expectedIntent: 'availability' }
    ];

    test.each(intentTestCases)('should classify "$query" as $expectedIntent', async ({ query, expectedIntent }) => {
      const result = await ragService.retrieveRelevantProducts(query);
      
      expect(result.products).toBeDefined();
      expect(result.retrievalStrategy).toBeDefined();
      // Intent should be reflected in strategy or metadata
    });
  });

  describe('Context-Dependent Queries', () => {
    const contextScenarios = [
      {
        setup: 'User asks about pumps',
        initialQuery: 'show me hydraulic pumps',
        followUpQuery: 'which one is most efficient',
        expectedBehavior: 'should analyze pumps from context'
      },
      {
        setup: 'User browses catalog then asks specific question',
        initialQuery: 'what products do you offer',
        followUpQuery: 'do you have generators',
        expectedBehavior: 'should search for generators independently'
      },
      {
        setup: 'User asks about pricing after product search',
        initialQuery: 'Caterpillar excavator',
        followUpQuery: 'how much does it cost',
        expectedBehavior: 'should provide pricing for excavators'
      }
    ];

    test.each(contextScenarios)('$expectedBehavior', async ({ initialQuery, followUpQuery }) => {
      // First query to establish context
      const initialResult = await ragService.retrieveRelevantProducts(initialQuery);
      
      const context = {
        lastQuery: initialQuery,
        conversationTurn: 2,
        previousProducts: initialResult.products
      };

      // Follow-up query with context
      const followUpResult = await ragService.retrieveRelevantProductsWithContext(
        followUpQuery,
        context
      );

      expect(followUpResult.products).toBeDefined();
      expect(followUpResult.retrievalStrategy).toBeDefined();
    });
  });

  describe('Query Complexity Handling', () => {
    it('should handle multi-part queries', async () => {
      const result = await ragService.retrieveRelevantProducts(
        'I need a hydraulic pump with high pressure rating and good fuel efficiency for industrial use'
      );
      
      expect(result.products).toBeDefined();
      expect(result.searchTerms).toContain('hydraulic');
      expect(result.searchTerms).toContain('pump');
    });

    it('should handle queries with specifications', async () => {
      const result = await ragService.retrieveRelevantProducts(
        'generator 50kW 3-phase diesel powered'
      );
      
      expect(result.products).toBeDefined();
      expect(result.searchTerms.length).toBeGreaterThan(1);
    });

    it('should handle conversational queries', async () => {
      const result = await ragService.retrieveRelevantProducts(
        'I am looking for something to help with construction work'
      );
      
      expect(result.products).toBeDefined();
      expect(result.retrievalStrategy).toBeDefined();
    });
  });
});
```

## 🔄 TDD Workflow {#tdd-workflow}

### Red-Green-Refactor Cycle for RAG Systems

#### 1. **Red Phase: Write Failing Tests**

```typescript
// Example: Adding support for technical specifications
describe('Technical Specification Queries', () => {
  it('should extract technical specs from queries', async () => {
    const result = await ragService.retrieveRelevantProducts('pump 5HP 3000RPM');
    
    expect(result.extractedSpecs).toEqual({
      power: '5HP',
      speed: '3000RPM'
    });
    expect(result.products).toMatchSpecifications(['5HP', '3000RPM']);
  });
});
```

#### 2. **Green Phase: Implement Minimal Solution**

```typescript
// Add to ragService.ts
private extractTechnicalSpecs(query: string): Record<string, string> {
  const specs: Record<string, string> = {};
  
  // HP pattern
  const hpMatch = query.match(/(\d+)\s*HP/i);
  if (hpMatch) specs.power = hpMatch[0];
  
  // RPM pattern
  const rpmMatch = query.match(/(\d+)\s*RPM/i);
  if (rpmMatch) specs.speed = rpmMatch[0];
  
  return specs;
}
```

#### 3. **Refactor Phase: Optimize and Enhance**

```typescript
// Enhanced version with better pattern matching
private extractTechnicalSpecs(query: string): TechnicalSpecs {
  const patterns = {
    power: /(\d+(?:\.\d+)?)\s*(HP|kW|MW)/gi,
    speed: /(\d+)\s*RPM/gi,
    pressure: /(\d+)\s*(PSI|bar|MPa)/gi,
    voltage: /(\d+)\s*V/gi
  };
  
  // More sophisticated extraction logic...
}
```

### Continuous Integration Pipeline

**File**: `.github/workflows/test.yml`

```yaml
name: TDD Pipeline

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:unit
      
      - name: Run integration tests
        run: npm run test:integration
        env:
          GOOGLE_API_KEY: ${{ secrets.GOOGLE_API_KEY }}
      
      - name: Run performance tests
        run: npm run test:performance
      
      - name: Check coverage
        run: npm run test:coverage
```

## 🎯 Quality Gates {#quality-gates}

### Test Coverage Requirements

```typescript
// jest.config.js
module.exports = {
  collectCoverageFrom: [
    'src/lib/**/*.ts',
    'src/app/api/**/*.ts'
  ],
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 90,
      lines: 85,
      statements: 85
    },
    './src/lib/ragService.ts': {
      branches: 95,
      functions: 100,
      lines: 95,
      statements: 95
    }
  }
};
```

### Performance Benchmarks

```typescript
// performance/benchmarks.test.ts
describe('Performance Benchmarks', () => {
  it('should meet response time SLA', async () => {
    const queries = [
      'hydraulic pump',
      'what products do you offer',
      'Caterpillar excavator pricing'
    ];
    
    for (const query of queries) {
      const start = performance.now();
      await ragService.retrieveRelevantProducts(query);
      const duration = performance.now() - start;
      
      expect(duration).toBeLessThan(1500); // 1.5s SLA
    }
  });
  
  it('should handle concurrent load', async () => {
    const concurrentQueries = Array(50).fill('pump');
    const promises = concurrentQueries.map(q => 
      ragService.retrieveRelevantProducts(q)
    );
    
    const start = performance.now();
    const results = await Promise.all(promises);
    const duration = performance.now() - start;
    
    expect(results).toHaveLength(50);
    expect(duration).toBeLessThan(5000); // 5s for 50 concurrent requests
  });
});
```

## 🤖 AI Agent Instructions {#ai-agent-instructions}

### For Development AI Assistants

When working on this RAG chatbot project, follow these TDD principles:

1. **Always write tests first** for new query handling features
2. **Test query intent detection** before implementing new intent types
3. **Validate context preservation** in multi-turn conversations
4. **Test edge cases** including malformed queries, empty inputs, and special characters
5. **Verify performance requirements** are met for all new features
6. **Ensure fallback strategies** are tested and working
7. **Mock external dependencies** (database, AI API) for reliable testing

### Test-First Development Commands

```bash
# Run specific test suites during development
npm run test:unit -- --watch
npm run test:integration -- --testNamePattern="catalog browse"
npm run test:performance -- --verbose
npm run test:coverage -- --collectCoverageFrom="src/lib/ragService.ts"
```

### Quality Checklist

Before merging any RAG-related changes:

- [ ] All existing tests pass
- [ ] New functionality has corresponding tests
- [ ] Test coverage meets thresholds (85%+ overall, 95%+ for core RAG logic)
- [ ] Performance tests pass within SLA requirements
- [ ] Edge cases are covered
- [ ] Context-aware scenarios are tested
- [ ] Error handling is validated

This TDD approach ensures your RAG chatbot maintains high reliability and can confidently handle diverse user queries while supporting continuous enhancement and maintenance.


