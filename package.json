{"name": "chatbot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "format": "prettier --write .", "format:check": "prettier --check .", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "test": "jest", "test:watch": "jest --watch", "validate:queries": "ts-node scripts/validate-query-handling.ts", "validate:all": "npm run lint && npm run test && npm run validate:queries"}, "dependencies": {"@google/genai": "^1.11.0", "@supabase/supabase-js": "^2.53.0", "axios": "^1.11.0", "clsx": "^2.1.1", "framer-motion": "^12.23.11", "lucide-react": "^0.534.0", "next": "15.4.4", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jest": "^29.5.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "eslint-config-prettier": "^10.1.8", "jest": "^29.5.0", "jest-environment-node": "^29.5.0", "prettier": "^3.6.2", "tailwindcss": "^4", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5"}}