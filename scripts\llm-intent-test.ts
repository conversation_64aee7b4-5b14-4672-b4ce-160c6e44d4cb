/**
 * Simple LLM intent detection test
 * Run with: pnpm exec ts-node scripts/llm-intent-test.ts
 */

import { GoogleGenAI } from '@google/genai';

async function detectIntentWithLLM(query: string): Promise<string> {
  try {
    const ai = new GoogleGenAI({ apiKey: process.env.GOOGLE_API_KEY || '' });

    const intentPrompt = `Analyze this user query for an industrial products chatbot and determine the intent.

User Query: "${query}"

Available Intents:
1. catalog_browse - User wants to see the full product catalog or general product overview
2. product_search - User is looking for specific products or categories  
3. availability - User is asking about stock status
4. pricing - User is asking about costs or prices
5. general - General conversation or greetings

Respond with ONLY the intent name (e.g., "catalog_browse").`;

    const result = await ai.models.generateContent({
      model: 'gemini-1.5-flash-latest',
      contents: intentPrompt,
      config: {
        temperature: 0.1,
      },
    });

    return result.text?.trim().toLowerCase() || 'error';
  } catch (error) {
    console.error('LLM error:', error);
    return 'error';
  }
}

const testQueries = [
  { query: 'What products do you offer?', expected: 'catalog_browse' },
  { query: 'What products do you have?', expected: 'catalog_browse' },
  { query: 'What items are available?', expected: 'catalog_browse' },
  { query: 'Show me your inventory', expected: 'catalog_browse' },
  { query: "What's your product range?", expected: 'catalog_browse' },
  { query: "I'd like to browse products", expected: 'catalog_browse' },
  { query: 'What equipment do you carry?', expected: 'catalog_browse' },
  { query: 'Hello', expected: 'general' },
  { query: 'I need a pump', expected: 'product_search' },
];

async function runLLMTest() {
  console.log('🤖 Testing LLM-Based Intent Detection\n');
  console.log('='.repeat(80));
  console.log('This demonstrates how LLMs understand semantic meaning\n');

  let correctCount = 0;
  let totalCount = 0;

  for (const test of testQueries) {
    const result = await detectIntentWithLLM(test.query);
    const isCorrect = result.includes(test.expected);

    totalCount++;
    if (isCorrect) correctCount++;

    const status = isCorrect ? '✅' : '❌';
    const resultText = isCorrect ? 'CORRECT' : 'WRONG';

    console.log(`${status} Query: "${test.query}"`);
    console.log(`   Expected: ${test.expected}`);
    console.log(`   Got: ${result} (${resultText})`);
    console.log('');
  }

  console.log('='.repeat(80));
  console.log(
    `📊 LLM Results: ${correctCount}/${totalCount} correct (${Math.round((correctCount / totalCount) * 100)}% accuracy)`
  );
  console.log('');

  console.log('🎯 Comparison Summary:');
  console.log('- Rule-based accuracy: 45% (5/11 correct)');
  console.log(
    `- LLM-based accuracy: ${Math.round((correctCount / totalCount) * 100)}% (${correctCount}/${totalCount} correct)`
  );
  console.log('');

  if (correctCount > 7) {
    console.log(
      '🎉 LLM significantly outperforms rule-based pattern matching!'
    );
  }
}

// Run the test
runLLMTest().catch(console.error);
