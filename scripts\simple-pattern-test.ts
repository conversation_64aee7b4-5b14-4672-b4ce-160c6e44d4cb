/**
 * Simple test to demonstrate rule-based pattern matching limitations
 * Run with: pnpm exec ts-node scripts/simple-pattern-test.ts
 */

// Simple rule-based pattern matching simulation
function detectIntentRuleBased(query: string): string {
  const normalizedQuery = query.toLowerCase().trim();
  
  // Catalog browsing patterns - exact substring matching (current approach)
  const catalogPatterns = [
    'what products do you offer',
    'what do you sell',
    'show me your products',
    "what's available",
    'product catalog',
    'full catalog',
    'all products',
    'what do you have',
    'product list',
    'inventory',
    'what can i buy',
  ];
  
  // Check patterns using exact substring matching
  if (catalogPatterns.some(pattern => normalizedQuery.includes(pattern))) {
    return 'catalog_browse';
  } else if (normalizedQuery.includes('available') || normalizedQuery.includes('stock')) {
    return 'availability';
  } else if (normalizedQuery.includes('price') || normalizedQuery.includes('cost')) {
    return 'pricing';
  } else {
    // Extract meaningful terms for product search
    const searchTerms = normalizedQuery
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(term => term.length > 2)
      .filter(term => !['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'me', 'you', 'what', 'show', 'i', 'need'].includes(term));
    
    if (searchTerms.length > 0) {
      return 'product_search';
    } else {
      return 'general';
    }
  }
}

const testQueries = [
  // Catalog browse variations - testing exact vs similar phrases
  { query: "What products do you offer?", expected: "catalog_browse", note: "Exact match - should work" },
  { query: "What products do you have?", expected: "catalog_browse", note: "Similar meaning - will likely fail" },
  { query: "What items are available?", expected: "catalog_browse", note: "Different words - will likely fail" },
  { query: "Show me your inventory", expected: "catalog_browse", note: "Different phrasing - will likely fail" },
  { query: "What's your product range?", expected: "catalog_browse", note: "Different structure - will likely fail" },
  { query: "I'd like to browse products", expected: "catalog_browse", note: "Natural language - will likely fail" },
  { query: "What do you have?", expected: "catalog_browse", note: "Exact match - should work" },
  { query: "What equipment do you carry?", expected: "catalog_browse", note: "Different words - will likely fail" },
  
  // Edge cases
  { query: "What's available?", expected: "catalog_browse", note: "Might be misclassified as availability" },
  { query: "Hello", expected: "general", note: "Should be general" },
  { query: "I need a pump", expected: "product_search", note: "Should be product search" },
];

function runPatternTest() {
  console.log('🧪 Testing Rule-Based Pattern Matching Limitations\n');
  console.log('=' .repeat(80));
  console.log('This demonstrates why exact substring matching fails for natural language\n');
  
  let correctCount = 0;
  let totalCount = 0;
  
  for (const test of testQueries) {
    const result = detectIntentRuleBased(test.query);
    const isCorrect = result === test.expected;
    
    totalCount++;
    if (isCorrect) correctCount++;
    
    const status = isCorrect ? '✅' : '❌';
    const resultText = isCorrect ? 'CORRECT' : 'WRONG';
    
    console.log(`${status} Query: "${test.query}"`);
    console.log(`   Expected: ${test.expected}`);
    console.log(`   Got: ${result} (${resultText})`);
    console.log(`   Note: ${test.note}`);
    console.log('');
  }
  
  console.log('=' .repeat(80));
  console.log(`📊 Results: ${correctCount}/${totalCount} correct (${Math.round(correctCount/totalCount*100)}% accuracy)`);
  console.log('');
  
  console.log('🔍 Key Observations:');
  console.log('- Exact matches work fine');
  console.log('- Similar meanings with different words fail');
  console.log('- Natural language variations fail');
  console.log('- Users must know exact phrases to get correct results');
  console.log('');
  
  console.log('💡 Why LLM-based intent detection would be better:');
  console.log('- Understands semantic meaning, not just text patterns');
  console.log('- Handles natural language variations');
  console.log('- No need to manually add every possible phrase');
  console.log('- Works with any way users naturally express their intent');
  console.log('');
  
  if (correctCount < totalCount * 0.8) {
    console.log('⚠️  Low accuracy demonstrates the need for LLM-based intent detection!');
  }
}

// Run the test
runPatternTest();
