/**
 * Test script to demonstrate LLM vs Rule-based intent detection
 * Run with: npx ts-node scripts/test-intent-detection.ts
 */

import { ragService } from '../src/lib/ragService';

const testQueries = [
  // Catalog browse variations
  "What products do you offer?",
  "What products do you have?", 
  "What items are available?",
  "Show me your inventory",
  "What's your product range?",
  "I'd like to browse products",
  "Help me see what you sell",
  "What equipment do you carry?",
  
  // Product search variations
  "I need a hydraulic pump",
  "Show me generators", 
  "Looking for welding equipment",
  "Do you have Caterpillar excavators?",
  
  // Availability variations
  "What's in stock?",
  "What can I buy today?",
  "Available generators",
  
  // Pricing variations
  "How much does it cost?",
  "Show me prices",
  "What's the price range?",
  
  // Edge cases
  "Hello",
  "Thanks",
  "I'm looking for something",
];

async function testIntentDetection() {
  console.log('🧪 Testing LLM vs Rule-Based Intent Detection\n');
  console.log('=' .repeat(80));
  
  for (const query of testQueries) {
    console.log(`\n📝 Query: "${query}"`);
    
    try {
      // Test with LLM enabled
      const llmService = new (await import('../src/lib/ragService')).RAGService({
        useLLMIntentDetection: true
      });
      
      const llmResult = await llmService.retrieveRelevantProducts(query);
      
      // Test with rule-based only
      const ruleService = new (await import('../src/lib/ragService')).RAGService({
        useLLMIntentDetection: false
      });
      
      const ruleResult = await ruleService.retrieveRelevantProducts(query);
      
      console.log(`   🤖 LLM Result: ${llmResult.retrievalStrategy}`);
      console.log(`   📏 Rule Result: ${ruleResult.retrievalStrategy}`);
      
      if (llmResult.retrievalStrategy !== ruleResult.retrievalStrategy) {
        console.log(`   ⚡ DIFFERENCE DETECTED!`);
      } else {
        console.log(`   ✅ Same result`);
      }
      
    } catch (error) {
      console.log(`   ❌ Error: ${error}`);
    }
  }
  
  console.log('\n' + '='.repeat(80));
  console.log('🎯 Test Complete');
  console.log('\nKey Observations:');
  console.log('- LLM should handle variations better');
  console.log('- Rule-based may miss nuanced queries');
  console.log('- Both should agree on clear-cut cases');
}

// Run the test
if (require.main === module) {
  testIntentDetection().catch(console.error);
}

export { testIntentDetection };
