/**
 * Comprehensive validation script for TDD Query Intent Handling
 * This script validates all scenarios from the TDD documentation
 */

import { ragService } from '../src/lib/ragService';

interface TestCase {
  query: string;
  expectedIntent: string;
  expectedStrategy: string;
  description: string;
  category: string;
}

const testCases: TestCase[] = [
  // 1. Catalog Browse Intent
  {
    query: "What products do you offer?",
    expectedIntent: "catalog_browse",
    expectedStrategy: "catalog_browse",
    description: "Basic catalog inquiry",
    category: "Catalog Browse"
  },
  {
    query: "Show me your catalog",
    expectedIntent: "catalog_browse", 
    expectedStrategy: "catalog_browse",
    description: "Catalog display request",
    category: "Catalog Browse"
  },
  {
    query: "What do you sell?",
    expectedIntent: "catalog_browse",
    expectedStrategy: "catalog_browse", 
    description: "Sales inquiry",
    category: "Catalog Browse"
  },
  {
    query: "Product list",
    expectedIntent: "catalog_browse",
    expectedStrategy: "catalog_browse",
    description: "Product listing request",
    category: "Catalog Browse"
  },
  {
    query: "Show me everything",
    expectedIntent: "catalog_browse",
    expectedStrategy: "catalog_browse",
    description: "Complete inventory request",
    category: "Catalog Browse"
  },

  // 2. Category Browse Intent
  {
    query: "What types of pumps do you have?",
    expectedIntent: "category_browse",
    expectedStrategy: "category_browse",
    description: "Category-specific inquiry",
    category: "Category Browse"
  },
  {
    query: "Show me all generators",
    expectedIntent: "category_browse",
    expectedStrategy: "category_browse",
    description: "Category display request",
    category: "Category Browse"
  },
  {
    query: "Categories of equipment",
    expectedIntent: "category_browse",
    expectedStrategy: "category_browse",
    description: "Equipment categories inquiry",
    category: "Category Browse"
  },

  // 3. Availability Intent
  {
    query: "What's in stock?",
    expectedIntent: "availability",
    expectedStrategy: "availability_focused",
    description: "Stock availability inquiry",
    category: "Availability"
  },
  {
    query: "Available generators",
    expectedIntent: "availability",
    expectedStrategy: "availability_focused",
    description: "Category-specific availability",
    category: "Availability"
  },
  {
    query: "What can I buy today?",
    expectedIntent: "availability",
    expectedStrategy: "availability_focused",
    description: "Immediate purchase inquiry",
    category: "Availability"
  },

  // 4. Pricing Intent
  {
    query: "How much does the pump cost?",
    expectedIntent: "pricing",
    expectedStrategy: "multi_term",
    description: "Specific price inquiry",
    category: "Pricing"
  },
  {
    query: "Show me products under $5000",
    expectedIntent: "pricing",
    expectedStrategy: "multi_term",
    description: "Budget-based search",
    category: "Pricing"
  },
  {
    query: "Compare prices",
    expectedIntent: "pricing",
    expectedStrategy: "multi_term",
    description: "Price comparison request",
    category: "Pricing"
  },

  // 5. Product Search Intent
  {
    query: "Industrial hydraulic pump",
    expectedIntent: "product_search",
    expectedStrategy: "multi_term",
    description: "Specific product search",
    category: "Product Search"
  },
  {
    query: "Caterpillar excavator",
    expectedIntent: "product_search",
    expectedStrategy: "exact_phrase",
    description: "Brand-specific search",
    category: "Product Search"
  },
  {
    query: "5HP motor",
    expectedIntent: "product_search",
    expectedStrategy: "multi_term",
    description: "Specification-based search",
    category: "Product Search"
  },

  // 6. Edge Cases
  {
    query: "",
    expectedIntent: "general",
    expectedStrategy: "featured_fallback",
    description: "Empty query",
    category: "Edge Cases"
  },
  {
    query: "Hello",
    expectedIntent: "general",
    expectedStrategy: "featured_fallback",
    description: "Greeting without product intent",
    category: "Edge Cases"
  },
  {
    query: "the and or but",
    expectedIntent: "general",
    expectedStrategy: "featured_fallback",
    description: "Only stop words",
    category: "Edge Cases"
  }
];

interface ConversationScenario {
  name: string;
  steps: Array<{
    query: string;
    expectedBehavior: string;
    context?: any;
  }>;
}

const conversationScenarios: ConversationScenario[] = [
  {
    name: "Catalog Browse After Specific Search",
    steps: [
      {
        query: "Show me hydraulic pumps",
        expectedBehavior: "Returns hydraulic pumps"
      },
      {
        query: "What products do you offer?",
        expectedBehavior: "Returns diverse catalog (independent query)"
      }
    ]
  },
  {
    name: "Follow-up Questions",
    steps: [
      {
        query: "What generators do you have?",
        expectedBehavior: "Returns generators"
      },
      {
        query: "Which one is most fuel efficient?",
        expectedBehavior: "Context-aware analysis of previous generators"
      }
    ]
  },
  {
    name: "Category Switching",
    steps: [
      {
        query: "Show me welding equipment",
        expectedBehavior: "Returns welding equipment"
      },
      {
        query: "What types of products do you have?",
        expectedBehavior: "Returns category overview (independent query)"
      }
    ]
  }
];

async function validateQueryHandling(): Promise<void> {
  console.log('🧪 Starting TDD Query Intent Validation\n');
  
  let passedTests = 0;
  let failedTests = 0;
  const failures: string[] = [];

  // Test individual query intents
  console.log('📋 Testing Individual Query Intents...\n');
  
  for (const testCase of testCases) {
    try {
      const result = await ragService.retrieveRelevantProducts(testCase.query);
      
      const passed = result.retrievalStrategy === testCase.expectedStrategy ||
                    (testCase.expectedStrategy === "multi_term" && 
                     ["exact_phrase", "multi_term", "single_term_fallback"].includes(result.retrievalStrategy));
      
      if (passed) {
        console.log(`✅ ${testCase.category}: "${testCase.query}" → ${result.retrievalStrategy}`);
        passedTests++;
      } else {
        console.log(`❌ ${testCase.category}: "${testCase.query}" → Expected: ${testCase.expectedStrategy}, Got: ${result.retrievalStrategy}`);
        failedTests++;
        failures.push(`${testCase.description}: Expected ${testCase.expectedStrategy}, got ${result.retrievalStrategy}`);
      }
    } catch (error) {
      console.log(`💥 ${testCase.category}: "${testCase.query}" → Error: ${error}`);
      failedTests++;
      failures.push(`${testCase.description}: Error - ${error}`);
    }
  }

  // Test conversation scenarios
  console.log('\n🗣️ Testing Conversation Scenarios...\n');
  
  for (const scenario of conversationScenarios) {
    console.log(`📖 Scenario: ${scenario.name}`);
    
    let context: any = undefined;
    
    for (let i = 0; i < scenario.steps.length; i++) {
      const step = scenario.steps[i];
      
      try {
        const result = await ragService.retrieveRelevantProductsWithContext(step.query, context);
        
        // Update context for next step
        if (i === 0) {
          context = {
            previousProducts: result.products,
            lastQuery: step.query,
            conversationTurn: 1
          };
        }
        
        console.log(`  ${i + 1}. "${step.query}" → ${result.retrievalStrategy} (${result.products.length} products)`);
        
        // Validate independent query detection
        if (step.query.includes("What products do you offer") || 
            step.query.includes("What types of products")) {
          if (result.retrievalStrategy === 'catalog_browse' || result.retrievalStrategy === 'category_browse') {
            console.log(`     ✅ Correctly detected as independent query`);
            passedTests++;
          } else {
            console.log(`     ❌ Should be independent query`);
            failedTests++;
            failures.push(`${scenario.name} - Step ${i + 1}: Should be independent query`);
          }
        } else {
          passedTests++;
        }
        
      } catch (error) {
        console.log(`     💥 Error: ${error}`);
        failedTests++;
        failures.push(`${scenario.name} - Step ${i + 1}: Error - ${error}`);
      }
    }
    console.log('');
  }

  // Test analytics
  console.log('📊 Testing Analytics...\n');
  
  try {
    const result = await ragService.retrieveRelevantProducts("Industrial pump");
    const analytics = ragService.getRetrievalAnalytics(result);
    
    const hasRequiredFields = analytics.hasOwnProperty('productCount') &&
                             analytics.hasOwnProperty('strategy') &&
                             analytics.hasOwnProperty('searchTermCount') &&
                             analytics.hasOwnProperty('hasResults');
    
    if (hasRequiredFields) {
      console.log(`✅ Analytics: All required fields present`);
      console.log(`   - Product Count: ${analytics.productCount}`);
      console.log(`   - Strategy: ${analytics.strategy}`);
      console.log(`   - Search Terms: ${analytics.searchTermCount}`);
      console.log(`   - Has Results: ${analytics.hasResults}`);
      passedTests++;
    } else {
      console.log(`❌ Analytics: Missing required fields`);
      failedTests++;
      failures.push("Analytics missing required fields");
    }
  } catch (error) {
    console.log(`💥 Analytics Error: ${error}`);
    failedTests++;
    failures.push(`Analytics error: ${error}`);
  }

  // Summary
  console.log('\n📈 Validation Summary');
  console.log('='.repeat(50));
  console.log(`✅ Passed: ${passedTests}`);
  console.log(`❌ Failed: ${failedTests}`);
  console.log(`📊 Success Rate: ${((passedTests / (passedTests + failedTests)) * 100).toFixed(1)}%`);
  
  if (failures.length > 0) {
    console.log('\n❌ Failures:');
    failures.forEach((failure, index) => {
      console.log(`   ${index + 1}. ${failure}`);
    });
  }
  
  if (failedTests === 0) {
    console.log('\n🎉 All tests passed! The chatbot can reliably handle all query types.');
  } else {
    console.log('\n⚠️ Some tests failed. Review the implementation for the failed cases.');
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  validateQueryHandling().catch(console.error);
}

export { validateQueryHandling, testCases, conversationScenarios };
