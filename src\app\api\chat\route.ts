import { GoogleGenAI } from '@google/genai';
import { supabase } from '@/lib/supabaseClient';

const MODEL_NAME = 'gemini-1.5-flash-latest';
const API_KEY = process.env.GOOGLE_API_KEY || '';

export async function POST(req: Request) {
  const ai = new GoogleGenAI({ apiKey: API_KEY });

  try {
    const { message, history } = await req.json();

    // Format history for Gemini API
    const formattedHistory = (history || []).map(
      (msg: { sender: 'user' | 'bot'; text: string }) => ({
        role: msg.sender === 'user' ? 'user' : 'model',
        parts: [{ text: msg.text }],
      })
    );

    // 1. Search for relevant products in Supabase
    const { data: products, error: dbError } = await supabase
      .from('products')
      .select('name, description, category, price, in_stock')
      .textSearch('fts', message.split(' ').join(' | '));

    if (dbError) {
      console.error('Supabase error:', dbError);
      throw new Error('Failed to fetch product data.');
    }

    // 2. Construct context information
    const context =
      products && products.length > 0
        ? `
Here is some information about our products:
${products
  .map(
    (p) =>
      `- Name: ${p.name}, Description: ${p.description}, Category: ${p.category}, Price: $${p.price}, In Stock: ${
        p.in_stock ? 'Yes' : 'No'
      }`
  )
  .join('\n')}
      `
        : 'I could not find any specific product information for that query. Please ask the user for more details or clarify their question if needed.';

    // 3. Create system instruction
    const systemInstruction = `You are a professional and helpful customer support assistant for an industrial products company.
Your role is to answer user questions based ONLY on the product information provided in the context and the conversation history.
Do not answer any questions that are not related to the products. If the information is not available, say that you cannot find the information and ask for more details.
Be concise and friendly.

Current product context:
${context}`;

    // 4. Start a chat session with history and system instruction
    const chat = ai.chats.create({
      model: MODEL_NAME,
      history: formattedHistory,
      config: {
        systemInstruction: systemInstruction,
      },
    });

    // 5. Send just the user's message (not a constructed prompt)
    const result = await chat.sendMessageStream({
      message: message,
    });

    const stream = new ReadableStream({
      async start(controller) {
        for await (const chunk of result) {
          const chunkText = chunk.text;
          controller.enqueue(new TextEncoder().encode(chunkText));
        }
        controller.close();
      },
    });

    return new Response(stream, {
      headers: { 'Content-Type': 'text/plain; charset=utf-8' },
    });
  } catch (error) {
    console.error(error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
