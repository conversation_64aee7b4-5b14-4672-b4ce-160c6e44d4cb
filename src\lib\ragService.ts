import { supabase } from './supabaseClient';

export interface Product {
  name: string;
  description: string;
  category: string;
  price: number;
  in_stock: boolean;
}

export interface RetrievalResult {
  products: Product[];
  searchTerms: string[];
  retrievalStrategy: string;
  isContextAware?: boolean;
}

export interface ConversationContext {
  previousProducts: Product[];
  lastQuery: string;
  conversationTurn: number;
}

export interface RAGConfig {
  maxResults: number;
  minTermLength: number;
  maxTerms: number;
  enableSemanticSearch: boolean;
  contextWindowSize: number;
}

const DEFAULT_CONFIG: RAGConfig = {
  maxResults: 10,
  minTermLength: 2,
  maxTerms: 10,
  enableSemanticSearch: false, // Can be enhanced with embeddings later
  contextWindowSize: 4000, // Approximate token limit for context
};

export class RAGService {
  private config: RAGConfig;

  constructor(config: Partial<RAGConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Enhanced query preprocessing with comprehensive intent detection
   */
  private preprocessQuery(query: string): {
    searchTerms: string[];
    intent:
      | 'catalog_browse'
      | 'product_search'
      | 'comparison'
      | 'availability'
      | 'pricing'
      | 'category_browse'
      | 'general';
    entities: string[];
    requiresFullCatalog: boolean;
  } {
    const normalizedQuery = query.toLowerCase().trim();

    // Extract search terms
    const searchTerms = normalizedQuery
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter((term) => term.length >= this.config.minTermLength)
      .filter((term) => !this.isStopWord(term))
      .slice(0, this.config.maxTerms);

    // Enhanced intent detection with catalog browsing
    let intent:
      | 'catalog_browse'
      | 'product_search'
      | 'comparison'
      | 'availability'
      | 'pricing'
      | 'category_browse'
      | 'general' = 'general';
    let requiresFullCatalog = false;

    // Catalog browsing patterns - comprehensive list
    const catalogPatterns = [
      'what products do you offer',
      'what do you sell',
      'show me your products',
      "what's available",
      'product catalog',
      'full catalog',
      'all products',
      'what do you have',
      'product list',
      'inventory',
      'what can i buy',
      'show me everything',
      'complete catalog',
      'entire inventory',
      'all items',
      'product range',
      "what's in stock",
      'browse products',
      'view catalog',
    ];

    // Category browsing patterns - enhanced
    const categoryPatterns = [
      'types of',
      'categories',
      'what kind of',
      'show me all',
      'categories of',
      'different types',
      'variety of',
      'range of',
      'selection of',
      'models of',
      'brands of',
    ];

    if (catalogPatterns.some((pattern) => normalizedQuery.includes(pattern))) {
      intent = 'catalog_browse';
      requiresFullCatalog = true;
    } else if (
      categoryPatterns.some((pattern) => normalizedQuery.includes(pattern))
    ) {
      intent = 'category_browse';
      requiresFullCatalog = true;
    } else if (
      normalizedQuery.includes('compare') ||
      normalizedQuery.includes('vs') ||
      normalizedQuery.includes('versus')
    ) {
      intent = 'comparison';
    } else if (
      normalizedQuery.includes('price') ||
      normalizedQuery.includes('cost') ||
      normalizedQuery.includes('$')
    ) {
      intent = 'pricing';
    } else if (
      normalizedQuery.includes('available') ||
      normalizedQuery.includes('stock') ||
      normalizedQuery.includes('in stock')
    ) {
      intent = 'availability';
    } else if (searchTerms.length > 0) {
      intent = 'product_search';
    }

    // Extract potential product entities (simple approach)
    const entities = searchTerms.filter((term) => term.length > 3);

    return { searchTerms, intent, entities, requiresFullCatalog };
  }

  /**
   * Enhanced stop word filtering with domain-specific terms
   */
  private isStopWord(word: string): boolean {
    const stopWords = new Set([
      // Common stop words
      'the',
      'a',
      'an',
      'and',
      'or',
      'but',
      'in',
      'on',
      'at',
      'to',
      'for',
      'of',
      'with',
      'by',
      'is',
      'are',
      'was',
      'were',
      'be',
      'been',
      'being',
      'have',
      'has',
      'had',
      'do',
      'does',
      'did',
      'will',
      'would',
      'could',
      'should',
      'may',
      'might',
      'can',
      'what',
      'where',
      'when',
      'why',
      'how',
      // Domain-specific stop words for better query processing
      'me',
      'you',
      'your',
      'my',
      'i',
      'we',
      'us',
      'our',
      'this',
      'that',
      'these',
      'those',
      'please',
      'thanks',
      'thank',
      'hello',
      'hi',
      'hey',
      'need',
      'want',
      'looking',
      'find',
      'get',
      'give',
      'tell',
      'help',
      'see',
      'know',
      'think',
      'like',
      'good',
      'best',
    ]);
    return stopWords.has(word);
  }

  /**
   * Context-aware retrieval that considers conversation history
   */
  async retrieveRelevantProductsWithContext(
    query: string,
    context?: ConversationContext
  ): Promise<RetrievalResult> {
    const { searchTerms, intent, entities, requiresFullCatalog } =
      this.preprocessQuery(query);

    // Check if this is a follow-up question that should ignore previous context
    const isIndependentQuery = this.isIndependentQuery(query, intent);

    if (isIndependentQuery || !context) {
      // Use standard retrieval for independent queries
      return this.retrieveRelevantProducts(query);
    }

    // For context-dependent queries, enhance with conversation context
    const enhancedResult = await this.retrieveRelevantProducts(query);
    enhancedResult.isContextAware = true;

    return enhancedResult;
  }

  /**
   * Determine if a query should be treated independently of previous context
   */
  private isIndependentQuery(query: string, intent: string): boolean {
    const independentPatterns = [
      'what products do you offer',
      'show me your catalog',
      'what do you sell',
      "what's available",
      'product list',
      'full catalog',
      'all products',
      'what categories',
      'types of products',
      'what do you have',
    ];

    const normalizedQuery = query.toLowerCase();

    // Catalog browsing intents are always independent
    if (intent === 'catalog_browse' || intent === 'category_browse') {
      return true;
    }

    // Check for explicit independent query patterns
    return independentPatterns.some((pattern) =>
      normalizedQuery.includes(pattern)
    );
  }

  /**
   * Multi-strategy retrieval with intent-based routing
   */
  async retrieveRelevantProducts(query: string): Promise<RetrievalResult> {
    const { searchTerms, intent, entities, requiresFullCatalog } =
      this.preprocessQuery(query);

    // Handle catalog browsing intents
    if (intent === 'catalog_browse' || requiresFullCatalog) {
      const products = await this.getCatalogOverview();
      return {
        products: products.slice(0, this.config.maxResults),
        searchTerms,
        retrievalStrategy: 'catalog_browse',
      };
    }

    // Handle category browsing
    if (intent === 'category_browse') {
      const products = await this.getCategoryOverview(searchTerms);
      return {
        products: products.slice(0, this.config.maxResults),
        searchTerms,
        retrievalStrategy: 'category_browse',
      };
    }

    // Handle availability-specific queries
    if (intent === 'availability') {
      const products = await this.getAvailableProducts(searchTerms);
      return {
        products: products.slice(0, this.config.maxResults),
        searchTerms,
        retrievalStrategy: 'availability_focused',
      };
    }

    // Handle queries with no meaningful search terms
    if (searchTerms.length === 0) {
      // For general queries without terms, show popular/featured products
      const products = await this.getFeaturedProducts();
      return {
        products: products.slice(0, this.config.maxResults),
        searchTerms: [],
        retrievalStrategy: 'featured_fallback',
      };
    }

    // Standard product search strategies
    // Strategy 1: Exact phrase matching (highest priority)
    let products = await this.exactPhraseSearch(query);
    if (products.length > 0) {
      return {
        products: products.slice(0, this.config.maxResults),
        searchTerms,
        retrievalStrategy: 'exact_phrase',
      };
    }

    // Strategy 2: Multi-term OR search
    products = await this.multiTermSearch(searchTerms);
    if (products.length > 0) {
      return {
        products: products.slice(0, this.config.maxResults),
        searchTerms,
        retrievalStrategy: 'multi_term',
      };
    }

    // Strategy 3: Single term fallback
    products = await this.singleTermFallback(searchTerms);
    return {
      products: products.slice(0, this.config.maxResults),
      searchTerms,
      retrievalStrategy: 'single_term_fallback',
    };
  }

  /**
   * Exact phrase search for high precision
   */
  private async exactPhraseSearch(query: string): Promise<Product[]> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('name, description, category, price, in_stock')
        .textSearch('fts', `"${query}"`)
        .limit(this.config.maxResults);

      if (error) {
        console.error('Exact phrase search error:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Exact phrase search exception:', error);
      return [];
    }
  }

  /**
   * Multi-term OR search for broader coverage
   */
  private async multiTermSearch(searchTerms: string[]): Promise<Product[]> {
    try {
      const searchQuery = searchTerms.join(' | ');

      const { data, error } = await supabase
        .from('products')
        .select('name, description, category, price, in_stock')
        .textSearch('fts', searchQuery)
        .limit(this.config.maxResults * 2); // Get more for ranking

      if (error) {
        console.error('Multi-term search error:', error);
        return [];
      }

      // Simple relevance scoring based on term matches
      const scoredProducts = (data || []).map((product) => {
        const productText =
          `${product.name} ${product.description} ${product.category}`.toLowerCase();
        const matchCount = searchTerms.filter((term) =>
          productText.includes(term)
        ).length;
        return { ...product, relevanceScore: matchCount };
      });

      // Sort by relevance and return
      return scoredProducts
        .sort((a, b) => b.relevanceScore - a.relevanceScore)
        .map(({ relevanceScore, ...product }) => product);
    } catch (error) {
      console.error('Multi-term search exception:', error);
      return [];
    }
  }

  /**
   * Single term fallback for maximum recall
   */
  private async singleTermFallback(searchTerms: string[]): Promise<Product[]> {
    try {
      // Try each term individually and combine results
      const allResults: Product[] = [];

      for (const term of searchTerms.slice(0, 3)) {
        // Limit to first 3 terms
        const { data, error } = await supabase
          .from('products')
          .select('name, description, category, price, in_stock')
          .textSearch('fts', term)
          .limit(5);

        if (!error && data) {
          allResults.push(...data);
        }
      }

      // Remove duplicates based on name
      const uniqueProducts = allResults.filter(
        (product, index, self) =>
          index === self.findIndex((p) => p.name === product.name)
      );

      return uniqueProducts;
    } catch (error) {
      console.error('Single term fallback exception:', error);
      return [];
    }
  }

  /**
   * Get catalog overview for "what products do you offer" type queries
   */
  private async getCatalogOverview(): Promise<Product[]> {
    try {
      // Get a diverse sample of products across categories
      const { data, error } = await supabase
        .from('products')
        .select('name, description, category, price, in_stock')
        .order('category')
        .limit(this.config.maxResults);

      if (error) {
        console.error('Catalog overview error:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Catalog overview exception:', error);
      return [];
    }
  }

  /**
   * Get category overview for browsing by category
   */
  private async getCategoryOverview(searchTerms: string[]): Promise<Product[]> {
    try {
      // If search terms exist, try to match categories first
      if (searchTerms.length > 0) {
        const categoryQuery = searchTerms.join(' | ');
        const { data, error } = await supabase
          .from('products')
          .select('name, description, category, price, in_stock')
          .textSearch('category', categoryQuery)
          .limit(this.config.maxResults);

        if (!error && data && data.length > 0) {
          return data;
        }
      }

      // Fallback to diverse category sampling
      return this.getCatalogOverview();
    } catch (error) {
      console.error('Category overview exception:', error);
      return [];
    }
  }

  /**
   * Get only available products for stock queries
   */
  private async getAvailableProducts(
    searchTerms: string[]
  ): Promise<Product[]> {
    try {
      if (searchTerms.length > 0) {
        const searchQuery = searchTerms.join(' | ');
        const { data, error } = await supabase
          .from('products')
          .select('name, description, category, price, in_stock')
          .textSearch('fts', searchQuery)
          .eq('in_stock', true)
          .limit(this.config.maxResults);

        if (!error && data) {
          return data;
        }
      }

      // Fallback to all available products
      const { data, error } = await supabase
        .from('products')
        .select('name, description, category, price, in_stock')
        .eq('in_stock', true)
        .limit(this.config.maxResults);

      return data || [];
    } catch (error) {
      console.error('Available products exception:', error);
      return [];
    }
  }

  /**
   * Get featured/popular products for general queries
   */
  private async getFeaturedProducts(): Promise<Product[]> {
    try {
      // Simple approach: get products ordered by price (assuming higher price = more featured)
      // In a real system, you might have a "featured" flag or popularity score
      const { data, error } = await supabase
        .from('products')
        .select('name, description, category, price, in_stock')
        .order('price', { ascending: false })
        .limit(this.config.maxResults);

      if (error) {
        console.error('Featured products error:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Featured products exception:', error);
      return [];
    }
  }

  /**
   * Format products for context injection with token management
   */
  formatProductsForContext(products: Product[]): string {
    if (!products || products.length === 0) {
      return '';
    }

    let context = '\n**Available Product Information:**\n';
    let currentLength = context.length;
    const maxLength = this.config.contextWindowSize;

    for (let i = 0; i < products.length; i++) {
      const product = products[i];
      const productText = `${i + 1}. **${product.name}**
   - Description: ${product.description}
   - Category: ${product.category}
   - Price: $${product.price}
   - In Stock: ${product.in_stock ? 'Yes' : 'No'}\n\n`;

      if (currentLength + productText.length > maxLength) {
        context += `... (${products.length - i} more products available)\n`;
        break;
      }

      context += productText;
      currentLength += productText.length;
    }

    return context + '---\n';
  }

  /**
   * Get retrieval analytics for monitoring
   */
  getRetrievalAnalytics(result: RetrievalResult): {
    productCount: number;
    strategy: string;
    searchTermCount: number;
    hasResults: boolean;
  } {
    return {
      productCount: result.products.length,
      strategy: result.retrievalStrategy,
      searchTermCount: result.searchTerms.length,
      hasResults: result.products.length > 0,
    };
  }
}

// Export singleton instance
export const ragService = new RAGService();
