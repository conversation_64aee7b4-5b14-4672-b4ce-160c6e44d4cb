import { RAGService } from '../src/lib/ragService';

// Mock Supabase client
jest.mock('../src/lib/supabaseClient', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        textSearch: jest.fn(() => ({
          limit: jest.fn(() => ({
            data: mockProducts,
            error: null
          })),
          eq: jest.fn(() => ({
            limit: jest.fn(() => ({
              data: mockProducts.filter(p => p.in_stock),
              error: null
            }))
          }))
        })),
        order: jest.fn(() => ({
          limit: jest.fn(() => ({
            data: mockProducts,
            error: null
          }))
        })),
        limit: jest.fn(() => ({
          data: mockProducts,
          error: null
        })),
        eq: jest.fn(() => ({
          limit: jest.fn(() => ({
            data: mockProducts.filter(p => p.in_stock),
            error: null
          }))
        }))
      }))
    }))
  }
}));

// Mock product data
const mockProducts = [
  {
    name: 'Industrial Hydraulic Pump HP-500',
    description: 'High-pressure hydraulic pump for industrial applications',
    category: 'Pumps',
    price: 2500,
    in_stock: true
  },
  {
    name: 'Caterpillar Excavator CAT-320',
    description: 'Heavy-duty excavator for construction',
    category: 'Construction Equipment',
    price: 85000,
    in_stock: true
  },
  {
    name: 'Industrial Generator GEN-750',
    description: 'Diesel generator 750kW capacity',
    category: 'Generators',
    price: 45000,
    in_stock: false
  },
  {
    name: 'Welding Machine WM-200',
    description: 'Professional welding equipment',
    category: 'Welding Equipment',
    price: 1200,
    in_stock: true
  }
];

describe('RAGService - TDD Query Intent Validation', () => {
  let ragService: RAGService;

  beforeEach(() => {
    ragService = new RAGService();
    jest.clearAllMocks();
  });

  describe('1. Catalog Browse Intent', () => {
    const catalogQueries = [
      'What products do you offer?',
      'Show me your catalog',
      'What do you sell?',
      'Product list',
      'What do you have?',
      'Full catalog',
      'All products',
      'What can I buy?'
    ];

    test.each(catalogQueries)('should detect catalog_browse intent for: "%s"', async (query) => {
      const result = await ragService.retrieveRelevantProducts(query);
      
      expect(result.retrievalStrategy).toBe('catalog_browse');
      expect(result.products).toBeDefined();
      expect(result.products.length).toBeGreaterThan(0);
    });

    test('should return diverse product sampling for catalog browse', async () => {
      const result = await ragService.retrieveRelevantProducts('What products do you offer?');
      
      expect(result.retrievalStrategy).toBe('catalog_browse');
      expect(result.products).toEqual(expect.arrayContaining([
        expect.objectContaining({ category: expect.any(String) })
      ]));
    });
  });

  describe('2. Category Browse Intent', () => {
    const categoryQueries = [
      'What types of pumps do you have?',
      'Show me all generators',
      'Categories of equipment',
      'Types of products'
    ];

    test.each(categoryQueries)('should detect category_browse intent for: "%s"', async (query) => {
      const result = await ragService.retrieveRelevantProducts(query);
      
      expect(['category_browse', 'product_search']).toContain(result.retrievalStrategy);
      expect(result.products).toBeDefined();
    });
  });

  describe('3. Availability Intent', () => {
    const availabilityQueries = [
      'What\'s in stock?',
      'Available generators',
      'What can I buy today?',
      'In stock products'
    ];

    test.each(availabilityQueries)('should detect availability intent for: "%s"', async (query) => {
      const result = await ragService.retrieveRelevantProducts(query);
      
      expect(result.retrievalStrategy).toBe('availability_focused');
      expect(result.products).toBeDefined();
      // All returned products should be in stock
      result.products.forEach(product => {
        expect(product.in_stock).toBe(true);
      });
    });
  });

  describe('4. Pricing Intent', () => {
    const pricingQueries = [
      'How much does the pump cost?',
      'Show me products under $5000',
      'Compare prices',
      'Price of motor',
      'Cost of equipment'
    ];

    test.each(pricingQueries)('should detect pricing intent for: "%s"', async (query) => {
      const result = await ragService.retrieveRelevantProducts(query);
      
      expect(result.retrievalStrategy).toMatch(/exact_phrase|multi_term|single_term_fallback/);
      expect(result.products).toBeDefined();
    });
  });

  describe('5. Product Search Intent', () => {
    const productSearchQueries = [
      'Industrial hydraulic pump',
      'Caterpillar excavator',
      '5HP motor',
      'Welding equipment'
    ];

    test.each(productSearchQueries)('should detect product_search intent for: "%s"', async (query) => {
      const result = await ragService.retrieveRelevantProducts(query);
      
      expect(result.retrievalStrategy).toMatch(/exact_phrase|multi_term|single_term_fallback/);
      expect(result.products).toBeDefined();
      expect(result.searchTerms.length).toBeGreaterThan(0);
    });
  });

  describe('6. Context-Aware Conversation Handling', () => {
    test('should treat catalog queries as independent', async () => {
      const context = {
        previousProducts: mockProducts.slice(0, 1), // Only hydraulic pump
        lastQuery: 'Show me hydraulic pumps',
        conversationTurn: 1
      };

      const result = await ragService.retrieveRelevantProductsWithContext(
        'What products do you offer?',
        context
      );

      expect(result.retrievalStrategy).toBe('catalog_browse');
      expect(result.products.length).toBeGreaterThan(1); // Should show diverse catalog, not just pumps
    });

    test('should preserve context for follow-up questions', async () => {
      const context = {
        previousProducts: mockProducts.filter(p => p.category === 'Generators'),
        lastQuery: 'Show me generators',
        conversationTurn: 1
      };

      const result = await ragService.retrieveRelevantProductsWithContext(
        'Which one is most efficient?',
        context
      );

      expect(result.isContextAware).toBe(true);
      expect(result.products).toBeDefined();
    });
  });

  describe('7. Fallback Strategies', () => {
    test('should return featured products for queries with no search terms', async () => {
      const result = await ragService.retrieveRelevantProducts('Hello');
      
      expect(result.retrievalStrategy).toBe('featured_fallback');
      expect(result.products).toBeDefined();
      expect(result.searchTerms).toEqual([]);
    });

    test('should handle empty queries gracefully', async () => {
      const result = await ragService.retrieveRelevantProducts('');
      
      expect(result.retrievalStrategy).toBe('featured_fallback');
      expect(result.products).toBeDefined();
    });

    test('should handle queries with only stop words', async () => {
      const result = await ragService.retrieveRelevantProducts('the and or but');
      
      expect(result.retrievalStrategy).toBe('featured_fallback');
      expect(result.searchTerms).toEqual([]);
    });
  });

  describe('8. Query Preprocessing', () => {
    test('should remove stop words correctly', async () => {
      const result = await ragService.retrieveRelevantProducts('Show me the best hydraulic pump');
      
      expect(result.searchTerms).not.toContain('the');
      expect(result.searchTerms).not.toContain('me');
      expect(result.searchTerms).toContain('hydraulic');
      expect(result.searchTerms).toContain('pump');
    });

    test('should extract meaningful terms', async () => {
      const result = await ragService.retrieveRelevantProducts('I need a 5HP industrial motor');
      
      expect(result.searchTerms).toContain('5hp');
      expect(result.searchTerms).toContain('industrial');
      expect(result.searchTerms).toContain('motor');
      expect(result.searchTerms).not.toContain('need');
    });

    test('should handle special characters and normalize text', async () => {
      const result = await ragService.retrieveRelevantProducts('What\'s the price of CAT-320?');
      
      expect(result.searchTerms).toContain('price');
      expect(result.searchTerms).toContain('cat');
      expect(result.searchTerms).toContain('320');
    });
  });

  describe('9. Analytics and Monitoring', () => {
    test('should provide retrieval analytics', async () => {
      const result = await ragService.retrieveRelevantProducts('Industrial pump');
      const analytics = ragService.getRetrievalAnalytics(result);
      
      expect(analytics).toHaveProperty('productCount');
      expect(analytics).toHaveProperty('strategy');
      expect(analytics).toHaveProperty('searchTermCount');
      expect(analytics).toHaveProperty('hasResults');
      
      expect(typeof analytics.productCount).toBe('number');
      expect(typeof analytics.strategy).toBe('string');
      expect(typeof analytics.searchTermCount).toBe('number');
      expect(typeof analytics.hasResults).toBe('boolean');
    });
  });

  describe('10. Edge Cases and Error Handling', () => {
    test('should handle very long queries', async () => {
      const longQuery = 'industrial hydraulic pump motor generator excavator welding equipment construction machinery heavy duty professional grade commercial'.repeat(5);
      
      const result = await ragService.retrieveRelevantProducts(longQuery);
      
      expect(result.products).toBeDefined();
      expect(result.searchTerms.length).toBeLessThanOrEqual(10); // Should limit terms
    });

    test('should handle special characters and unicode', async () => {
      const result = await ragService.retrieveRelevantProducts('Motör with spëcial chars & symbols!@#$%');
      
      expect(result.products).toBeDefined();
      expect(result.retrievalStrategy).toBeDefined();
    });

    test('should handle mixed case queries', async () => {
      const result = await ragService.retrieveRelevantProducts('INDUSTRIAL Hydraulic PUMP');
      
      expect(result.searchTerms).toContain('industrial');
      expect(result.searchTerms).toContain('hydraulic');
      expect(result.searchTerms).toContain('pump');
    });
  });
});
